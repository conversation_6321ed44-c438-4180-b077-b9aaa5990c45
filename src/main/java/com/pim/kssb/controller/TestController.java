package com.pim.kssb.controller;

import com.pim.kssb.service.TestKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Autowired
    private TestKafkaProducer testKafkaProducer;

    @PostMapping("/send-message")
    public String sendTestMessage() {
        log.info("Received request to send test message");
        testKafkaProducer.sendTestMessage();
        return "Test message sent to Kafka topic";
    }
}
