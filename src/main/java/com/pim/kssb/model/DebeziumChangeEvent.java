package com.pim.kssb.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DebeziumChangeEvent {
    
    @JsonProperty("before")
    private Map<String, Object> before;
    
    @JsonProperty("after")
    private Map<String, Object> after;
    
    @JsonProperty("source")
    private Source source;
    
    @JsonProperty("op")
    private String operation;
    
    @JsonProperty("ts_ms")
    private Long timestamp;
    
    @JsonProperty("transaction")
    private Transaction transaction;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Source {
        @JsonProperty("version")
        private String version;
        
        @JsonProperty("connector")
        private String connector;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("ts_ms")
        private Long timestamp;
        
        @JsonProperty("snapshot")
        private String snapshot;
        
        @JsonProperty("db")
        private String database;
        
        @JsonProperty("sequence")
        private String sequence;
        
        @JsonProperty("schema")
        private String schema;
        
        @JsonProperty("table")
        private String table;
        
        @JsonProperty("txId")
        private Long transactionId;
        
        @JsonProperty("lsn")
        private Long lsn;
        
        @JsonProperty("xmin")
        private Long xmin;
    }
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Transaction {
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("total_order")
        private Long totalOrder;
        
        @JsonProperty("data_collection_order")
        private Long dataCollectionOrder;
    }
}
