package com.pim.kssb.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pim.kssb.model.DebeziumChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

@Service
@Slf4j
public class DebeziumKafkaConsumer {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @KafkaListener(topics = "debezium.debezium.fahasa.m_product", groupId = "kssb-consumer-group")
    public void consumeProductChanges(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.RECEIVED_TIMESTAMP) long timestamp,
            ConsumerRecord<String, String> record,
            Acknowledgment acknowledgment) {
        
        try {
            log.info("=== Received Debezium Change Event ===");
            log.info("Topic: {}", topic);
            log.info("Partition: {}", partition);
            log.info("Offset: {}", record.offset());
            log.info("Timestamp: {} ({})", timestamp, 
                LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault()));
            log.info("Key: {}", record.key());
            
            // Parse the Debezium change event
            DebeziumChangeEvent changeEvent = objectMapper.readValue(message, DebeziumChangeEvent.class);
            
            // Print operation type
            log.info("Operation: {}", changeEvent.getOperation());
            
            // Print source information
            if (changeEvent.getSource() != null) {
                log.info("Source - Database: {}, Schema: {}, Table: {}", 
                    changeEvent.getSource().getDatabase(),
                    changeEvent.getSource().getSchema(),
                    changeEvent.getSource().getTable());
            }
            
            // Print before state (for UPDATE and DELETE operations)
            if (changeEvent.getBefore() != null && !changeEvent.getBefore().isEmpty()) {
                log.info("Before: {}", formatProductData(changeEvent.getBefore()));
            }
            
            // Print after state (for CREATE and UPDATE operations)
            if (changeEvent.getAfter() != null && !changeEvent.getAfter().isEmpty()) {
                log.info("After: {}", formatProductData(changeEvent.getAfter()));
            }
            
            // Print transaction information if available
            if (changeEvent.getTransaction() != null) {
                log.info("Transaction ID: {}", changeEvent.getTransaction().getId());
            }
            
            log.info("Raw message: {}", message);
            log.info("=====================================");
            
            // Acknowledge the message
            if (acknowledgment != null) {
                acknowledgment.acknowledge();
            }
            
        } catch (JsonProcessingException e) {
            log.error("Error parsing Debezium change event: {}", e.getMessage());
            log.error("Raw message: {}", message);
        } catch (Exception e) {
            log.error("Error processing Debezium change event: {}", e.getMessage(), e);
        }
    }
    
    private String formatProductData(Map<String, Object> data) {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        
        // Format the specific fields we're interested in based on the connector config
        if (data.containsKey("m_product_id")) {
            sb.append("m_product_id=").append(data.get("m_product_id")).append(", ");
        }
        if (data.containsKey("value")) {
            sb.append("value=").append(data.get("value")).append(", ");
        }
        if (data.containsKey("name")) {
            sb.append("name=").append(data.get("name")).append(", ");
        }
        
        // Add any other fields
        data.forEach((key, value) -> {
            if (!key.equals("m_product_id") && !key.equals("value") && !key.equals("name")) {
                sb.append(key).append("=").append(value).append(", ");
            }
        });
        
        // Remove trailing comma and space
        if (sb.length() > 1 && sb.substring(sb.length() - 2).equals(", ")) {
            sb.setLength(sb.length() - 2);
        }
        
        sb.append("}");
        return sb.toString();
    }
}
