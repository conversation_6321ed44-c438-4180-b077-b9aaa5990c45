package com.pim.kssb.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TestKafkaProducer {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendTestMessage() {
        String topic = "debezium.debezium.fahasa.m_product";
        String testMessage = """
            {
              "before": null,
              "after": {
                "m_product_id": 9999997,
                "value": "TEST-MANUAL-001",
                "name": "Manual Test Product",
                "m_attributesetinstance_id": 1000003
              },
              "source": {
                "version": "2.4.2.Final",
                "connector": "postgresql",
                "name": "debezium",
                "ts_ms": 1748276909953,
                "snapshot": "false",
                "db": "postgres",
                "sequence": "[null,\\"83193832\\"]",
                "schema": "fahasa",
                "table": "m_product",
                "txId": 797,
                "lsn": 83193832,
                "xmin": null
              },
              "op": "c",
              "ts_ms": 1748276909953,
              "transaction": null
            }
            """;
        
        log.info("Sending test message to topic: {}", topic);
        kafkaTemplate.send(topic, "test-key", testMessage);
        log.info("Test message sent successfully");
    }
}
