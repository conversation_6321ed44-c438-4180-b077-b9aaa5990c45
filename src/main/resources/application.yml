spring:
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: kssb-consumer-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

logging:
  level:
    com.pim.kssb: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
